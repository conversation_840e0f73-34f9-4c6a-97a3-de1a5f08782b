import React from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Search } from "lucide-react";

interface Topic {
  id: number;
  title: string;
  type: string; // Changed from department
  category: string; // New field
  createdAt: string;
  applicants: number;
  status: string;
}

interface TopicsTabProps {
  topics: Topic[];
  searchTerm: string;
  onSearchChange: (value: string) => void;
  selectedType: string; // Changed from selectedDepartment
  onTypeChange: (value: string) => void; // Changed from onDepartmentChange
  selectedCategory: string; // New prop
  onCategoryChange: (value: string) => void; // New prop
  selectedStatus: string;
  onStatusChange: (value: string) => void;
  onViewApplicants: (topicId: number) => void;
}

export const TopicsTab: React.FC<TopicsTabProps> = ({
  topics,
  searchTerm,
  onSearchChange,
  selectedType, // Changed from selectedDepartment
  onTypeChange, // Changed from onDepartmentChange
  selectedCategory, // New prop
  onCategoryChange, // New prop
  selectedStatus,
  onStatusChange,
  onViewApplicants,
}) => {
  // Get unique types for filter (changed from departments)
  const types = ["all", ...new Set(topics.map((topic) => topic.type))];

  // Get unique categories for filter (new)
  const categories = ["all", ...new Set(topics.map((topic) => topic.category))];

  // Get unique statuses for filter (unchanged)
  const statuses = ["all", ...new Set(topics.map((topic) => topic.status))];

  return (
    <Card>
      <CardHeader>
        <CardTitle>Research Topics</CardTitle>
        <CardDescription>
          Review research topics and their Principal Investigator applicants
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col md:flex-row gap-4 mb-6">
          <div className="relative flex-1">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search topics..."
              className="pl-8"
              value={searchTerm}
              onChange={(e) => onSearchChange(e.target.value)}
            />
          </div>
          <div className="flex flex-col sm:flex-row gap-4">
            <Select
              value={selectedType} // Changed from selectedDepartment
              onValueChange={onTypeChange} // Changed from onDepartmentChange
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Type" />
              </SelectTrigger>
              <SelectContent>
                {types.map(
                  (
                    type // Changed from departments
                  ) => (
                    <SelectItem key={type} value={type}>
                      {type === "all" ? "All Types" : type}{" "}
                      {/* Changed label */}
                    </SelectItem>
                  )
                )}
              </SelectContent>
            </Select>
            <Select value={selectedCategory} onValueChange={onCategoryChange}>
              {" "}
              {/* New filter */}
              <SelectTrigger className="w-[200px]">
                <SelectValue placeholder="Category" />
              </SelectTrigger>
              <SelectContent>
                {categories.map((category) => (
                  <SelectItem key={category} value={category}>
                    {category === "all" ? "All Categories" : category}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={selectedStatus} onValueChange={onStatusChange}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                {statuses.map((status) => (
                  <SelectItem key={status} value={status}>
                    {status === "all" ? "All Statuses" : status}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Topic Title</TableHead>
                <TableHead>Type</TableHead> {/* Changed from Department */}
                <TableHead>Category</TableHead> {/* New column */}
                <TableHead>Created Date</TableHead>
                <TableHead>Applicants</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {topics.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} className="h-24 text-center">
                    {" "}
                    {/* Updated colSpan from 6 to 7 */}
                    No topics found.
                  </TableCell>
                </TableRow>
              ) : (
                topics.map((topic) => (
                  <TableRow key={topic.id}>
                    <TableCell className="font-medium">{topic.title}</TableCell>
                    <TableCell>{topic.type}</TableCell>{" "}
                    {/* Changed from topic.department */}
                    <TableCell>{topic.category}</TableCell> {/* New cell */}
                    <TableCell>{topic.createdAt}</TableCell>
                    <TableCell>
                      <Badge variant="outline">{topic.applicants}</Badge>
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant="outline"
                        className={
                          topic.status === "Waiting for PI"
                            ? "bg-amber-100 text-amber-800 border-amber-200"
                            : "bg-emerald-100 text-emerald-800 border-emerald-200"
                        }
                      >
                        {topic.status}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onViewApplicants(topic.id)}
                        disabled={topic.applicants === 0}
                      >
                        {topic.applicants > 0
                          ? "View Applicants"
                          : "No Applicants"}
                      </Button>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
};
