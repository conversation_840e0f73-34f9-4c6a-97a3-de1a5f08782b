import React, { memo, useMemo } from "react";
import { FormType } from "../constants";
import { BM1Form } from "./forms/BM1Form";
import { BM2Form } from "./forms/BM2Form";
import { BM3Form } from "./forms/BM3Form";
import { BM4Form } from "./forms/BM4Form";
import { BM5Form } from "./forms/BM5Form";

// Base FormData interface
interface BaseFormData {
  [key: string]: string | number | boolean;
}

// Specific form data interfaces
interface BM1FormData extends BaseFormData {
  projectTitle?: string;
  principalInvestigator?: string;
  institution?: string;
  researchObjectives?: string;
  methodology?: string;
  expectedOutcomes?: string;
  budget?: string | number;
  duration?: string | number;
  additionalNotes?: string;
}

interface BM2FormData extends BaseFormData {
  meetingDate?: string;
  meetingTime?: string;
  meetingLocation?: string;
  chairperson?: string;
  attendees?: string;
  projectTitle?: string;
  principalInvestigator?: string;
  discussionPoints?: string;
  recommendations?: string;
  decision?: string;
  nextSteps?: string;
  additionalNotes?: string;
}

interface BM3FormData extends BaseFormData {
  projectTitle?: string;
  principalInvestigator?: string;
  institution?: string;
  projectPeriod?: string;
  totalBudget?: string;
  executiveSummary?: string;
  objectives?: string;
  methodology?: string;
  results?: string;
  conclusions?: string;
  impact?: string;
  publications?: string;
  futureWork?: string;
  additionalNotes?: string;
}

interface BM4FormData extends BaseFormData {
  projectTitle?: string;
  principalInvestigator?: string;
  reportingPeriod?: string;
  reportDate?: string;
  projectPhase?: string;
  progressSummary?: string;
  completedActivities?: string;
  achievements?: string;
  currentStatus?: string;
  challenges?: string;
  solutions?: string;
  upcomingActivities?: string;
  timeline?: string;
  budgetUtilized?: string;
  budgetRemaining?: string;
  resourceNeeds?: string;
  additionalNotes?: string;
}

interface BM5FormData extends BaseFormData {
  projectTitle?: string;
  principalInvestigator?: string;
  requestDate?: string;
  projectPhase?: string;
  urgency?: string;
  changeType?: string;
  changeDescription?: string;
  currentSituation?: string;
  justification?: string;
  impact?: string;
  risks?: string;
  implementationPlan?: string;
  proposedStartDate?: string;
  estimatedDuration?: string;
  resourceRequirements?: string;
  additionalCosts?: string;
  costSavings?: string;
  budgetJustification?: string;
  additionalNotes?: string;
}

// Union type for all form data
type FormData =
  | BM1FormData
  | BM2FormData
  | BM3FormData
  | BM4FormData
  | BM5FormData;

interface FormContentProps {
  formType: FormType;
  formData: FormData;
  onDataChange: (data: FormData) => void;
  onSubmit: () => void;
}

export const FormContent: React.FC<FormContentProps> = memo(
  ({ formType, formData, onDataChange, onSubmit }) => {
    const commonProps = useMemo(
      () => ({
        formData,
        onDataChange,
        onSubmit,
      }),
      [formData, onDataChange, onSubmit]
    );

    const renderForm = () => {
      switch (formType) {
        case "BM1":
          return (
            <BM1Form
              formData={formData as BM1FormData}
              onDataChange={onDataChange as (data: BM1FormData) => void}
              onSubmit={onSubmit}
            />
          );
        case "BM2":
          return (
            <BM2Form
              formData={formData as BM2FormData}
              onDataChange={onDataChange as (data: BM2FormData) => void}
              onSubmit={onSubmit}
            />
          );
        case "BM3":
          return (
            <BM3Form
              formData={formData as BM3FormData}
              onDataChange={onDataChange as (data: BM3FormData) => void}
              onSubmit={onSubmit}
            />
          );
        case "BM4":
          return (
            <BM4Form
              formData={formData as BM4FormData}
              onDataChange={onDataChange as (data: BM4FormData) => void}
              onSubmit={onSubmit}
            />
          );
        case "BM5":
          return (
            <BM5Form
              formData={formData as BM5FormData}
              onDataChange={onDataChange as (data: BM5FormData) => void}
              onSubmit={onSubmit}
            />
          );
        default:
          return (
            <div className="flex items-center justify-center py-12">
              <div className="text-center">
                <p className="text-muted-foreground text-lg">Form not found</p>
                <p className="text-sm text-muted-foreground mt-2">
                  Please select a valid form type.
                </p>
              </div>
            </div>
          );
      }
    };

    return <div className="space-y-6">{renderForm()}</div>;
  }
);
